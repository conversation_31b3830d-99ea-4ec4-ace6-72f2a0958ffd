#!/usr/bin/env python3
"""
Data All in One - Livestream Analytics
Chương trình phân tích dữ liệu livestream với PyQt6 và SQLite
"""

import sys
import logging
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from main_window import MainWindow

def setup_logging():
    """Thiết lập logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('app.log'),
            logging.StreamHandler()
        ]
    )

def main():
    """Hàm main của ứng dụng"""
    # Setup logging
    setup_logging()
    
    # Tạo QApplication
    app = QApplication(sys.argv)
    app.setApplicationName("Data All in One - Livestream Analytics")
    app.setApplicationVersion("1.0.0")
    
    # Thiết lập style
    app.setStyle('Fusion')
    
    # Tạo và hiển thị main window
    try:
        window = MainWindow()
        window.show()
        
        logging.info("Ứng dụng đã khởi động thành công")
        
        # Chạy event loop
        sys.exit(app.exec())
        
    except Exception as e:
        logging.error(f"Lỗi khởi động ứng dụng: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
