from PyQt6.QtCore import QThread, pyqtSignal, QObject
from typing import List, Dict
import logging

class ImportWorker(QThread):
    """Worker thread cho việc import dữ liệu"""
    
    # Signals
    progress_updated = pyqtSignal(int)  # Phần trăm hoàn thành
    status_updated = pyqtSignal(str)    # Thông báo trạng thái
    import_completed = pyqtSignal(bool, str)  # Kết quả import (success, message)
    
    def __init__(self, database_manager, file_path: str, kol_name: str, livestream_date: str):
        super().__init__()
        self.database_manager = database_manager
        self.file_path = file_path
        self.kol_name = kol_name
        self.livestream_date = livestream_date
        
    def run(self):
        """Thực hiện import dữ liệu"""
        try:
            self.status_updated.emit("<PERSON>ang bắt đầu import dữ liệu...")
            self.progress_updated.emit(10)
            
            self.status_updated.emit("Đang đọc file...")
            self.progress_updated.emit(30)
            
            # Thực hiện import
            success, message = self.database_manager.import_data_from_file(
                self.file_path, self.kol_name, self.livestream_date
            )
            
            self.progress_updated.emit(90)
            self.status_updated.emit("Hoàn tất import...")
            
            self.progress_updated.emit(100)
            self.import_completed.emit(success, message)
            
        except Exception as e:
            logging.error(f"Import worker error: {e}")
            self.import_completed.emit(False, f"Lỗi import: {str(e)}")


class QueryWorker(QThread):
    """Worker thread cho việc query dữ liệu"""
    
    # Signals
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    query_completed = pyqtSignal(dict)  # Kết quả query
    
    def __init__(self, query_engine, table_names: List[str], time_hours: List[int], 
                 date_filter: str = "all", limit: int = 400):
        super().__init__()
        self.query_engine = query_engine
        self.table_names = table_names
        self.time_hours = time_hours
        self.date_filter = date_filter
        self.limit = limit
        
    def run(self):
        """Thực hiện query dữ liệu"""
        try:
            self.status_updated.emit("Đang bắt đầu phân tích dữ liệu...")
            self.progress_updated.emit(10)
            
            total_hours = len(self.time_hours)
            results = {}
            
            for i, hour in enumerate(self.time_hours):
                self.status_updated.emit(f"Đang phân tích khung giờ {hour:02d}:00-{(hour+1)%24:02d}:00...")
                
                # Query cho từng khung giờ
                hour_result = self.query_engine.get_top_items_by_timeframe(
                    self.table_names, [hour], self.date_filter, self.limit
                )
                
                results.update(hour_result)
                
                # Cập nhật progress
                progress = 10 + int((i + 1) / total_hours * 80)
                self.progress_updated.emit(progress)
            
            self.status_updated.emit("Hoàn tất phân tích dữ liệu")
            self.progress_updated.emit(100)
            self.query_completed.emit(results)
            
        except Exception as e:
            logging.error(f"Query worker error: {e}")
            self.query_completed.emit({})
