import sqlite3
import os
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging

class DatabaseManager:
    def __init__(self):
        # Tạo đường dẫn lưu trữ trong AppData
        self.app_data_path = os.path.join(
            os.environ.get('LOCALAPPDATA', os.path.expanduser('~')),
            'Data All in One',
            'Data Analysist'
        )
        os.makedirs(self.app_data_path, exist_ok=True)
        
        self.db_path = os.path.join(self.app_data_path, 'livestream_data.db')
        self.init_database()
        
    def init_database(self):
        """Khởi tạo database và tạo bảng metadata"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Bảng metadata để theo dõi các bảng dữ liệu
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS table_metadata (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    table_name TEXT UNIQUE NOT NULL,
                    kol_name TEXT NOT NULL,
                    livestream_date TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    record_count INTEGER DEFAULT 0
                )
            ''')
            conn.commit()
    
    def create_data_table(self, table_name: str) -> bool:
        """Tạo bảng dữ liệu với schema chuẩn"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute(f'''
                    CREATE TABLE IF NOT EXISTS {table_name} (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        item_id VARCHAR(100) NOT NULL,
                        shop_id VARCHAR(100),
                        brand TEXT,
                        item_name TEXT,
                        time_hour INTEGER NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # Tạo indexes riêng
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_{table_name}_item_id ON {table_name}(item_id)')
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_{table_name}_time_hour ON {table_name}(time_hour)')
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_{table_name}_shop_id ON {table_name}(shop_id)')
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Error creating table {table_name}: {e}")
            return False
    
    def import_data_from_file(self, file_path: str, kol_name: str, livestream_date: str) -> Tuple[bool, str]:
        """Import dữ liệu từ file CSV hoặc Excel"""
        try:
            # Đọc file
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            elif file_path.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(file_path)
            else:
                return False, "Định dạng file không được hỗ trợ"
            
            # Validate columns
            required_columns = ['Item ID', 'Shop ID', 'Brand', 'Item name', 'Time']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return False, f"Thiếu các cột: {', '.join(missing_columns)}"
            
            # Tạo tên bảng
            table_name = f"{kol_name}_{livestream_date.replace('-', '_')}"
            
            # Tạo bảng
            if not self.create_data_table(table_name):
                return False, "Không thể tạo bảng"
            
            # Xử lý dữ liệu
            processed_data = []
            for _, row in df.iterrows():
                time_str = str(row['Time'])
                hour = self.extract_hour_from_time(time_str)
                
                processed_data.append({
                    'item_id': str(row['Item ID']),
                    'shop_id': str(row['Shop ID']) if pd.notna(row['Shop ID']) else '',
                    'brand': str(row['Brand']) if pd.notna(row['Brand']) else '',
                    'item_name': str(row['Item name']) if pd.notna(row['Item name']) else '',
                    'time_hour': hour
                })
            
            # Insert dữ liệu
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for data in processed_data:
                    cursor.execute(f'''
                        INSERT INTO {table_name} (item_id, shop_id, brand, item_name, time_hour)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (data['item_id'], data['shop_id'], data['brand'], 
                          data['item_name'], data['time_hour']))
                
                # Cập nhật metadata
                cursor.execute('''
                    INSERT OR REPLACE INTO table_metadata 
                    (table_name, kol_name, livestream_date, record_count)
                    VALUES (?, ?, ?, ?)
                ''', (table_name, kol_name, livestream_date, len(processed_data)))
                
                conn.commit()
            
            return True, f"Import thành công {len(processed_data)} bản ghi vào bảng {table_name}"
            
        except Exception as e:
            logging.error(f"Error importing data: {e}")
            return False, f"Lỗi import: {str(e)}"
    
    def extract_hour_from_time(self, time_str: str) -> int:
        """Trích xuất giờ từ chuỗi thời gian"""
        try:
            # Xử lý các format thời gian khác nhau
            if ':' in time_str:
                hour = int(time_str.split(':')[0])
                # Xử lý trường hợp 00:00 (nửa đêm)
                return hour if hour != 0 else 24
            else:
                # Nếu chỉ là số
                hour = int(float(time_str))
                return hour if hour != 0 else 24
        except:
            return 0  # Default hour nếu không parse được
    
    def get_all_tables(self) -> List[Dict]:
        """Lấy danh sách tất cả bảng dữ liệu"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT table_name, kol_name, livestream_date, record_count, created_at
                FROM table_metadata
                ORDER BY created_at DESC
            ''')
            
            results = cursor.fetchall()
            return [
                {
                    'table_name': row[0],
                    'kol_name': row[1],
                    'livestream_date': row[2],
                    'record_count': row[3],
                    'created_at': row[4]
                }
                for row in results
            ]
    
    def get_tables_by_kol(self, kol_name: str) -> List[str]:
        """Lấy danh sách bảng theo tên KOL"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            if kol_name.lower() == 'all':
                cursor.execute('SELECT table_name FROM table_metadata')
            else:
                cursor.execute('SELECT table_name FROM table_metadata WHERE kol_name = ?', (kol_name,))
            
            return [row[0] for row in cursor.fetchall()]
