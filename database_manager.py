import sqlite3
import os
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging

class DatabaseManager:
    def __init__(self):
        # Tạo đường dẫn lưu trữ trong AppData
        self.app_data_path = os.path.join(
            os.environ.get('LOCALAPPDATA', os.path.expanduser('~')),
            'Data All in One',
            'Data Analysist'
        )
        os.makedirs(self.app_data_path, exist_ok=True)
        
        self.db_path = os.path.join(self.app_data_path, 'livestream_data.db')
        self.init_database()
        
    def init_database(self):
        """Khởi tạo database và tạo bảng metadata"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Bảng metadata để theo dõi các bảng dữ liệu
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS table_metadata (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    table_name TEXT UNIQUE NOT NULL,
                    kol_name TEXT NOT NULL,
                    livestream_date TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    record_count INTEGER DEFAULT 0
                )
            ''')
            conn.commit()
    
    def create_gmv_table(self, table_name: str, time_columns: List[str]) -> bool:
        """Tạo bảng dữ liệu với cấu trúc GMV theo thời gian"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute(f'''
                    CREATE TABLE IF NOT EXISTS {table_name} (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        item_id VARCHAR(100) NOT NULL,
                        shop_id VARCHAR(100),
                        brand TEXT,
                        item_name TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # Tạo indexes riêng
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_{table_name}_item_id ON {table_name}(item_id)')
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_{table_name}_shop_id ON {table_name}(shop_id)')
                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Error creating table {table_name}: {e}")
            return False
    
    def import_data_from_file(self, file_path: str, kol_name: str, livestream_date: str) -> Tuple[bool, str]:
        """Import dữ liệu từ file CSV hoặc Excel với cấu trúc GMV theo thời gian"""
        try:
            # Đọc file
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            elif file_path.endswith(('.xlsx', '.xls')):
                df = pd.read_excel(file_path)
            else:
                return False, "Định dạng file không được hỗ trợ"

            # Validate basic columns
            required_columns = ['Item ID', 'Shop ID', 'Brand', 'Item name']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return False, f"Thiếu các cột: {', '.join(missing_columns)}"

            # Tìm các cột thời gian (format: DD-MM:HH:MM:SS)
            time_columns = []
            for col in df.columns:
                if self.is_time_column(col):
                    time_columns.append(col)

            if not time_columns:
                return False, "Không tìm thấy cột thời gian với format DD-MM:HH:MM:SS"

            # Tạo tên bảng
            table_name = f"{kol_name}_{livestream_date.replace('-', '_')}"

            # Tạo bảng với cấu trúc mới
            if not self.create_gmv_table(table_name, time_columns):
                return False, "Không thể tạo bảng"

            # Xử lý và insert dữ liệu
            processed_count = 0
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                for _, row in df.iterrows():
                    # Chuẩn bị dữ liệu cơ bản
                    item_id = str(row['Item ID']) if pd.notna(row['Item ID']) else ''
                    shop_id = str(row['Shop ID']) if pd.notna(row['Shop ID']) else ''
                    brand = str(row['Brand']) if pd.notna(row['Brand']) else ''
                    item_name = str(row['Item name']) if pd.notna(row['Item name']) else ''

                    if not item_id:  # Skip nếu không có Item ID
                        continue

                    # Chuẩn bị dữ liệu GMV cho các cột thời gian
                    gmv_values = []
                    for col in time_columns:
                        gmv = float(row[col]) if pd.notna(row[col]) and str(row[col]).replace('.','').replace(',','').isdigit() else 0.0
                        gmv_values.append(gmv)

                    # Tạo câu SQL insert
                    placeholders = ', '.join(['?'] * (4 + len(time_columns)))
                    sql = f'''
                        INSERT INTO {table_name}
                        (item_id, shop_id, brand, item_name, {', '.join([f'gmv_{i}' for i in range(len(time_columns))])})
                        VALUES ({placeholders})
                    '''

                    values = [item_id, shop_id, brand, item_name] + gmv_values
                    cursor.execute(sql, values)
                    processed_count += 1

                # Lưu metadata
                cursor.execute('''
                    INSERT OR REPLACE INTO table_metadata
                    (table_name, kol_name, livestream_date, record_count)
                    VALUES (?, ?, ?, ?)
                ''', (table_name, kol_name, livestream_date, processed_count))

                # Lưu thông tin cột thời gian
                cursor.execute(f'''
                    CREATE TABLE IF NOT EXISTS {table_name}_time_columns (
                        id INTEGER PRIMARY KEY,
                        column_name TEXT,
                        hour INTEGER,
                        original_header TEXT
                    )
                ''')

                for i, col in enumerate(time_columns):
                    hour = self.extract_hour_from_header(col)
                    cursor.execute(f'''
                        INSERT INTO {table_name}_time_columns
                        (column_name, hour, original_header)
                        VALUES (?, ?, ?)
                    ''', (f'gmv_{i}', hour, col))

                conn.commit()

            return True, f"Import thành công {processed_count} items với {len(time_columns)} khung thời gian"

        except Exception as e:
            logging.error(f"Error importing data: {e}")
            return False, f"Lỗi import: {str(e)}"
    
    def is_time_column(self, column_name: str) -> bool:
        """Kiểm tra xem cột có phải là cột thời gian không"""
        try:
            # Format: DD-MM:HH:MM:SS (ví dụ: 15-08:11:15:00)
            if ':' in column_name and '-' in column_name:
                parts = column_name.split(':')
                if len(parts) >= 2:
                    # Kiểm tra phần đầu có format DD-MM:HH
                    date_time_part = parts[0] + ':' + parts[1]
                    if '-' in date_time_part:
                        return True
            return False
        except:
            return False

    def extract_hour_from_header(self, header: str) -> int:
        """Trích xuất giờ từ header cột (format: DD-MM:HH:MM:SS)"""
        try:
            # Ví dụ: "15-08:11:15:00" -> lấy giờ 11
            if ':' in header and '-' in header:
                parts = header.split(':')
                if len(parts) >= 2:
                    # Lấy phần sau dấu '-' và trước dấu ':' thứ hai
                    time_part = parts[1]  # "11"
                    hour = int(time_part)
                    return hour if hour != 0 else 24
            return 0
        except:
            return 0

    def create_gmv_table(self, table_name: str, time_columns: List[str]) -> bool:
        """Tạo bảng với cấu trúc GMV theo thời gian"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Tạo các cột GMV động
                gmv_columns = []
                for i in range(len(time_columns)):
                    gmv_columns.append(f'gmv_{i} REAL DEFAULT 0')

                gmv_columns_sql = ', '.join(gmv_columns)

                cursor.execute(f'''
                    CREATE TABLE IF NOT EXISTS {table_name} (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        item_id VARCHAR(100) NOT NULL,
                        shop_id VARCHAR(100),
                        brand TEXT,
                        item_name TEXT,
                        {gmv_columns_sql},
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')

                # Tạo indexes
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_{table_name}_item_id ON {table_name}(item_id)')
                cursor.execute(f'CREATE INDEX IF NOT EXISTS idx_{table_name}_shop_id ON {table_name}(shop_id)')

                conn.commit()
                return True
        except Exception as e:
            logging.error(f"Error creating GMV table {table_name}: {e}")
            return False
    
    def get_all_tables(self) -> List[Dict]:
        """Lấy danh sách tất cả bảng dữ liệu"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT table_name, kol_name, livestream_date, record_count, created_at
                FROM table_metadata
                ORDER BY created_at DESC
            ''')
            
            results = cursor.fetchall()
            return [
                {
                    'table_name': row[0],
                    'kol_name': row[1],
                    'livestream_date': row[2],
                    'record_count': row[3],
                    'created_at': row[4]
                }
                for row in results
            ]
    
    def get_tables_by_kol(self, kol_name: str) -> List[str]:
        """Lấy danh sách bảng theo tên KOL"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            if kol_name.lower() == 'all':
                cursor.execute('SELECT table_name FROM table_metadata')
            else:
                cursor.execute('SELECT table_name FROM table_metadata WHERE kol_name = ?', (kol_name,))
            
            return [row[0] for row in cursor.fetchall()]
