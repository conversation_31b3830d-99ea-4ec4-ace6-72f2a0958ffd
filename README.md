# Data All in One - Livestream Analytics

Chương trình phân tích dữ liệu livestream với giao diện PyQt6 và database SQLite.

## Tính năng chính

### 1. Import dữ liệu
- Hỗ trợ file CSV và Excel (.xlsx, .xls)
- Tự động validate schema dữ liệu
- Lưu trữ theo format bảng: `{KOL_name}_{date}`
- Xử lý đặc biệt khung giờ 00:00 (nửa đêm)

### 2. Phân tích dữ liệu
- Query top 400 items theo khung giờ được chọn
- Hỗ trợ nhiều tùy chọn thời gian:
  - 3 tháng gần nhất
  - 6 tháng gần nhất  
  - 1 năm gần nhất
  - Cùng kỳ năm trước
  - Tất cả dữ liệu
- Cho phép chọn nhiều khung giờ cùng lúc
- Hỗ trợ query theo KOL cụ thể hoặc "all"

### 3. <PERSON><PERSON><PERSON> thị kết quả
- <PERSON><PERSON>n thị theo tabs cho từng khung giờ
- Co<PERSON> kết quả vào clipboard (mỗi ID một dòng)
- Giao diện thân thiện, không block UI

### 4. Lưu trữ dữ liệu
- Database SQLite lưu tại: `%LOCALAPPDATA%\Data All in One\Data Analysist\`
- Schema chuẩn với indexing tối ưu
- Metadata tracking cho các bảng

## Schema dữ liệu

Các file import cần có các cột:
- **Item ID**: VARCHAR(100) - Mã sản phẩm
- **Shop ID**: VARCHAR(100) - Mã shop
- **Brand**: TEXT - Thương hiệu
- **Item name**: TEXT - Tên sản phẩm  
- **Time**: Thời gian (format HH:MM)

## Cài đặt

### 1. Cài đặt Python dependencies
```bash
pip install -r requirements.txt
```

### 2. Chạy chương trình
```bash
python main.py
```

### 3. Tạo dữ liệu mẫu để test
```bash
python test_sample_data.py
```

## Hướng dẫn sử dụng

### Import dữ liệu
1. Nhấn "Chọn File" để chọn file CSV/Excel
2. Nhập tên KOL
3. Chọn ngày livestream
4. Nhấn "Import Dữ Liệu"

### Phân tích dữ liệu
1. Nhập tên KOL hoặc "all" để phân tích tất cả
2. Chọn khoảng thời gian cần phân tích
3. Chọn các khung giờ cần xem (10:00-00:00)
4. Nhấn "Phân Tích"

### Xem kết quả
1. Kết quả hiển thị theo tabs cho từng khung giờ
2. Nhấn vào tab để xem chi tiết
3. Nhấn "Copy Kết Quả" để copy Item IDs vào clipboard

## Cấu trúc dự án

```
├── main.py                 # File chính để chạy ứng dụng
├── main_window.py          # Giao diện chính PyQt6
├── database_manager.py     # Quản lý SQLite database
├── query_engine.py         # Engine phân tích và query dữ liệu
├── worker_threads.py       # Threading để không block UI
├── test_sample_data.py     # Tạo dữ liệu mẫu
├── requirements.txt        # Python dependencies
└── README.md              # Tài liệu này
```

## Lưu ý đặc biệt

- **Khung giờ 00:00**: Được tính là của ngày livestream được chọn (không phải ngày hôm sau)
- **Threading**: Tất cả operations import/query chạy background, không block UI
- **Database**: Tự động tạo indexes để tối ưu performance
- **Error handling**: Comprehensive error handling và logging

## Yêu cầu hệ thống

- Python 3.8+
- Windows (đã test), Linux/Mac (chưa test)
- RAM: Tối thiểu 4GB (khuyến nghị 8GB+ cho dataset lớn)
- Disk: 100MB+ cho database

## Troubleshooting

### Lỗi import file
- Kiểm tra file có đúng format CSV/Excel
- Đảm bảo có đầy đủ các cột bắt buộc
- Kiểm tra encoding file (khuyến nghị UTF-8)

### Lỗi database
- Kiểm tra quyền ghi vào thư mục AppData
- Xóa file database cũ nếu bị corrupt: `%LOCALAPPDATA%\Data All in One\Data Analysist\livestream_data.db`

### Performance chậm
- Giảm số lượng khung giờ query cùng lúc
- Sử dụng filter thời gian để giảm dataset
- Kiểm tra RAM và disk space
