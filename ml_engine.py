import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Tuple
import logging
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score
import joblib
import os

class MLEngine:
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.models = {}
        self.scalers = {}
        self.label_encoders = {}
        self.feature_columns = [
            'hour', 'day_of_week', 'is_weekend', 'month', 'season',
            'historical_avg_gmv', 'historical_frequency', 'days_since_last_appearance',
            'brand_performance', 'shop_performance', 'item_consistency_score'
        ]
        self.model_dir = self._get_model_dir()
        
    def _get_model_dir(self) -> str:
        """<PERSON><PERSON><PERSON> <PERSON><PERSON> mục lưu models"""
        app_data = os.path.join(os.environ.get('LOCALAPPDATA', ''), 'Data All in One', 'Data Analysist')
        model_dir = os.path.join(app_data, 'ml_models')
        os.makedirs(model_dir, exist_ok=True)
        return model_dir
    
    def extract_features(self, table_names: List[str]) -> pd.DataFrame:
        """Trích xuất features từ dữ liệu lịch sử"""
        try:
            all_data = []
            
            with sqlite3.connect(self.db_path) as conn:
                for table_name in table_names:
                    # Lấy metadata
                    cursor = conn.cursor()
                    cursor.execute('''
                        SELECT livestream_date, kol_name FROM table_metadata 
                        WHERE table_name = ?
                    ''', (table_name,))
                    
                    metadata = cursor.fetchone()
                    if not metadata:
                        continue
                    
                    livestream_date = datetime.strptime(metadata[0], '%Y-%m-%d')
                    kol_name = metadata[1]
                    
                    # Lấy thông tin cột thời gian
                    cursor.execute(f'''
                        SELECT column_name, hour, original_header
                        FROM {table_name}_time_columns
                        ORDER BY hour
                    ''')
                    
                    time_columns_info = cursor.fetchall()
                    if not time_columns_info:
                        continue
                    
                    # Lấy dữ liệu items
                    cursor.execute(f'''
                        SELECT item_id, shop_id, brand, item_name, {', '.join([col[0] for col in time_columns_info])}
                        FROM {table_name}
                    ''')
                    
                    items_data = cursor.fetchall()
                    
                    # Xử lý từng item
                    for item_row in items_data:
                        item_id = item_row[0]
                        shop_id = item_row[1]
                        brand = item_row[2]
                        item_name = item_row[3]
                        gmv_values = item_row[4:]
                        
                        # Tính GMV delta cho từng khung giờ
                        for i, (col_name, hour, original_header) in enumerate(time_columns_info):
                            if i < len(gmv_values):
                                current_gmv = gmv_values[i] or 0
                                prev_gmv = gmv_values[i-1] if i > 0 else 0
                                gmv_delta = current_gmv - prev_gmv
                                
                                if gmv_delta > 0:  # Chỉ lấy những khung giờ có bán hàng
                                    features = self._calculate_features(
                                        item_id, shop_id, brand, hour, livestream_date, 
                                        gmv_delta, table_names, conn
                                    )
                                    
                                    features.update({
                                        'table_name': table_name,
                                        'kol_name': kol_name,
                                        'target_gmv': gmv_delta
                                    })
                                    
                                    all_data.append(features)
            
            return pd.DataFrame(all_data)
            
        except Exception as e:
            logging.error(f"Error extracting features: {e}")
            return pd.DataFrame()
    
    def _calculate_features(self, item_id: str, shop_id: str, brand: str, hour: int, 
                          livestream_date: datetime, gmv_delta: float, 
                          table_names: List[str], conn) -> Dict:
        """Tính toán features cho một item"""
        
        # Basic time features
        day_of_week = livestream_date.weekday()  # 0=Monday, 6=Sunday
        is_weekend = 1 if day_of_week >= 5 else 0
        month = livestream_date.month
        season = (month - 1) // 3 + 1  # 1=Spring, 2=Summer, 3=Fall, 4=Winter
        
        # Historical performance features
        historical_data = self._get_historical_performance(
            item_id, shop_id, brand, hour, livestream_date, table_names, conn
        )
        
        return {
            'item_id': item_id,
            'shop_id': shop_id,
            'brand': brand,
            'hour': hour,
            'day_of_week': day_of_week,
            'is_weekend': is_weekend,
            'month': month,
            'season': season,
            'historical_avg_gmv': historical_data['avg_gmv'],
            'historical_frequency': historical_data['frequency'],
            'days_since_last_appearance': historical_data['days_since_last'],
            'brand_performance': historical_data['brand_avg'],
            'shop_performance': historical_data['shop_avg'],
            'item_consistency_score': historical_data['consistency']
        }
    
    def _get_historical_performance(self, item_id: str, shop_id: str, brand: str, 
                                  hour: int, current_date: datetime, 
                                  table_names: List[str], conn) -> Dict:
        """Lấy thông tin performance lịch sử"""
        cursor = conn.cursor()
        
        # Lấy dữ liệu lịch sử của item này
        item_history = []
        brand_history = []
        shop_history = []
        
        for table_name in table_names:
            try:
                # Lấy ngày của bảng
                cursor.execute('''
                    SELECT livestream_date FROM table_metadata 
                    WHERE table_name = ?
                ''', (table_name,))
                
                result = cursor.fetchone()
                if not result:
                    continue
                
                table_date = datetime.strptime(result[0], '%Y-%m-%d')
                if table_date >= current_date:  # Chỉ lấy dữ liệu trước ngày hiện tại
                    continue
                
                # Lấy thông tin cột thời gian cho khung giờ này
                cursor.execute(f'''
                    SELECT column_name FROM {table_name}_time_columns
                    WHERE hour = ?
                    ORDER BY id
                ''', (hour,))
                
                time_cols = cursor.fetchall()
                if not time_cols:
                    continue
                
                # Tính GMV delta cho item này
                for col_info in time_cols:
                    col_name = col_info[0]
                    cursor.execute(f'''
                        SELECT {col_name} FROM {table_name}
                        WHERE item_id = ? AND shop_id = ?
                    ''', (item_id, shop_id))
                    
                    gmv_result = cursor.fetchone()
                    if gmv_result and gmv_result[0]:
                        item_history.append(gmv_result[0])
                
                # Lấy performance của brand và shop
                cursor.execute(f'''
                    SELECT AVG({time_cols[0][0]}) FROM {table_name}
                    WHERE brand = ? AND {time_cols[0][0]} > 0
                ''', (brand,))
                
                brand_result = cursor.fetchone()
                if brand_result and brand_result[0]:
                    brand_history.append(brand_result[0])
                
                cursor.execute(f'''
                    SELECT AVG({time_cols[0][0]}) FROM {table_name}
                    WHERE shop_id = ? AND {time_cols[0][0]} > 0
                ''', (shop_id,))
                
                shop_result = cursor.fetchone()
                if shop_result and shop_result[0]:
                    shop_history.append(shop_result[0])
                    
            except Exception as e:
                logging.warning(f"Error processing table {table_name}: {e}")
                continue
        
        # Tính toán metrics
        avg_gmv = np.mean(item_history) if item_history else 0
        frequency = len(item_history)
        days_since_last = (current_date - datetime.now()).days if item_history else 999
        brand_avg = np.mean(brand_history) if brand_history else 0
        shop_avg = np.mean(shop_history) if shop_history else 0
        consistency = 1 - (np.std(item_history) / (avg_gmv + 1)) if len(item_history) > 1 else 0
        
        return {
            'avg_gmv': avg_gmv,
            'frequency': frequency,
            'days_since_last': days_since_last,
            'brand_avg': brand_avg,
            'shop_avg': shop_avg,
            'consistency': consistency
        }
    
    def train_models(self, table_names: List[str]) -> bool:
        """Train ML models cho từng khung giờ"""
        try:
            logging.info("Starting ML model training...")
            
            # Extract features
            df = self.extract_features(table_names)
            if df.empty:
                logging.warning("No data available for training")
                return False
            
            logging.info(f"Extracted {len(df)} training samples")
            
            # Train model cho từng khung giờ
            for hour in range(10, 25):
                hour_data = df[df['hour'] == hour].copy()
                
                if len(hour_data) < 10:  # Cần ít nhất 10 samples
                    logging.warning(f"Not enough data for hour {hour}: {len(hour_data)} samples")
                    continue
                
                # Prepare features
                X = self._prepare_features(hour_data)
                y = hour_data['target_gmv'].values
                
                if len(X) == 0:
                    continue
                
                # Split data
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=0.2, random_state=42
                )
                
                # Scale features
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)
                
                # Train model
                model = GradientBoostingRegressor(
                    n_estimators=100,
                    learning_rate=0.1,
                    max_depth=6,
                    random_state=42
                )
                
                model.fit(X_train_scaled, y_train)
                
                # Evaluate
                y_pred = model.predict(X_test_scaled)
                mse = mean_squared_error(y_test, y_pred)
                r2 = r2_score(y_test, y_pred)
                
                logging.info(f"Hour {hour} model - MSE: {mse:.2f}, R2: {r2:.3f}")
                
                # Save model
                self.models[hour] = model
                self.scalers[hour] = scaler
                
                # Save to disk
                model_path = os.path.join(self.model_dir, f'model_hour_{hour}.joblib')
                scaler_path = os.path.join(self.model_dir, f'scaler_hour_{hour}.joblib')
                
                joblib.dump(model, model_path)
                joblib.dump(scaler, scaler_path)
            
            logging.info("ML model training completed")
            return True
            
        except Exception as e:
            logging.error(f"Error training models: {e}")
            return False
    
    def _prepare_features(self, df: pd.DataFrame) -> np.ndarray:
        """Chuẩn bị features cho training"""
        try:
            # Encode categorical variables
            df_encoded = df.copy()
            
            for col in ['shop_id', 'brand']:
                if col in df_encoded.columns:
                    if col not in self.label_encoders:
                        self.label_encoders[col] = LabelEncoder()
                        df_encoded[col] = self.label_encoders[col].fit_transform(df_encoded[col].astype(str))
                    else:
                        # Handle unseen labels
                        known_labels = set(self.label_encoders[col].classes_)
                        df_encoded[col] = df_encoded[col].astype(str).apply(
                            lambda x: self.label_encoders[col].transform([x])[0] if x in known_labels else -1
                        )
            
            # Select numeric features
            numeric_features = []
            for col in self.feature_columns:
                if col in df_encoded.columns:
                    numeric_features.append(col)
            
            return df_encoded[numeric_features].values
            
        except Exception as e:
            logging.error(f"Error preparing features: {e}")
            return np.array([])

    def load_models(self) -> bool:
        """Load trained models từ disk"""
        try:
            for hour in range(10, 25):
                model_path = os.path.join(self.model_dir, f'model_hour_{hour}.joblib')
                scaler_path = os.path.join(self.model_dir, f'scaler_hour_{hour}.joblib')

                if os.path.exists(model_path) and os.path.exists(scaler_path):
                    self.models[hour] = joblib.load(model_path)
                    self.scalers[hour] = joblib.load(scaler_path)

            logging.info(f"Loaded {len(self.models)} ML models")
            return len(self.models) > 0

        except Exception as e:
            logging.error(f"Error loading models: {e}")
            return False

    def predict_item_performance(self, item_id: str, shop_id: str, brand: str,
                               hour: int, target_date: datetime,
                               table_names: List[str]) -> float:
        """Predict GMV performance cho item trong khung giờ cụ thể"""
        try:
            if hour not in self.models:
                return 0.0

            with sqlite3.connect(self.db_path) as conn:
                # Calculate features
                features = self._calculate_features(
                    item_id, shop_id, brand, hour, target_date,
                    0, table_names, conn  # gmv_delta=0 vì đang predict
                )

                # Prepare feature vector
                feature_vector = []
                for col in self.feature_columns:
                    if col in features:
                        value = features[col]

                        # Handle categorical encoding
                        if col in ['shop_id', 'brand'] and col in self.label_encoders:
                            known_labels = set(self.label_encoders[col].classes_)
                            if str(value) in known_labels:
                                value = self.label_encoders[col].transform([str(value)])[0]
                            else:
                                value = -1  # Unknown label

                        feature_vector.append(value)
                    else:
                        feature_vector.append(0)

                # Scale and predict
                X = np.array([feature_vector])
                X_scaled = self.scalers[hour].transform(X)
                prediction = self.models[hour].predict(X_scaled)[0]

                return max(0, prediction)  # Không cho phép prediction âm

        except Exception as e:
            logging.error(f"Error predicting for item {item_id}: {e}")
            return 0.0

    def get_ml_recommendations(self, table_names: List[str], target_hours: List[int],
                             target_date: datetime = None, limit: int = 500) -> Dict[int, List[Dict]]:
        """Lấy recommendations sử dụng ML models"""
        try:
            if not self.models:
                if not self.load_models():
                    logging.warning("No ML models available, falling back to traditional method")
                    return {}

            if target_date is None:
                target_date = datetime.now()

            results = {}

            with sqlite3.connect(self.db_path) as conn:
                # Lấy tất cả unique items từ database
                all_items = set()
                for table_name in table_names:
                    cursor = conn.cursor()
                    cursor.execute(f'''
                        SELECT DISTINCT item_id, shop_id, brand, item_name
                        FROM {table_name}
                    ''')

                    items = cursor.fetchall()
                    for item in items:
                        all_items.add(item)

                # Predict performance cho từng khung giờ
                for hour in target_hours:
                    if hour not in self.models:
                        results[hour] = []
                        continue

                    item_predictions = []

                    for item_id, shop_id, brand, item_name in all_items:
                        predicted_gmv = self.predict_item_performance(
                            item_id, shop_id, brand, hour, target_date, table_names
                        )

                        if predicted_gmv > 0:
                            item_predictions.append({
                                'item_id': item_id,
                                'shop_id': shop_id,
                                'brand': brand,
                                'item_name': item_name,
                                'predicted_gmv': predicted_gmv,
                                'avg_gmv': predicted_gmv,  # For compatibility
                                'source': 'ML_Prediction'
                            })

                    # Sort by predicted GMV và lấy top items
                    item_predictions.sort(key=lambda x: x['predicted_gmv'], reverse=True)
                    results[hour] = item_predictions[:limit]

                    logging.info(f"ML predictions for hour {hour}: {len(results[hour])} items")

            return results

        except Exception as e:
            logging.error(f"Error getting ML recommendations: {e}")
            return {}

    def should_retrain(self, table_names: List[str]) -> bool:
        """Kiểm tra xem có nên retrain models không"""
        try:
            # Check if models exist
            if not self.models:
                return True

            # Check if có data mới
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT COUNT(*) FROM table_metadata
                    WHERE created_at > datetime('now', '-7 days')
                ''')

                new_tables = cursor.fetchone()[0]
                return new_tables > 0

        except Exception as e:
            logging.error(f"Error checking retrain condition: {e}")
            return False
