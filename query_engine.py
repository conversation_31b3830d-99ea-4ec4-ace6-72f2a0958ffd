import sqlite3
from datetime import datetime, timedelta
from typing import List, Dict, Tu<PERSON>, Optional
import logging

class QueryEngine:
    def __init__(self, db_path: str):
        self.db_path = db_path
    
    def get_top_items_by_timeframe(self, 
                                   table_names: List[str], 
                                   time_hours: List[int], 
                                   date_filter: str = "all",
                                   limit: int = 400) -> Dict[int, List[Dict]]:
        """
        Lấy top items theo khung giờ
        
        Args:
            table_names: <PERSON>h sách tên bảng
            time_hours: <PERSON>h sách giờ cần query (10-24)
            date_filter: "3months", "6months", "1year", "same_period_last_year", "all"
            limit: Số lượng items tối đa (default 400)
        
        Returns:
            Dict với key là giờ, value là list items
        """
        results = {}
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for hour in time_hours:
                    # <PERSON><PERSON> lý giờ 00:00 (24)
                    query_hour = 24 if hour == 0 else hour
                    
                    # Tạo query cho từng khung giờ
                    union_queries = []
                    for table_name in table_names:
                        # <PERSON><PERSON>m tra bảng có tồn tại không
                        cursor.execute('''
                            SELECT name FROM sqlite_master 
                            WHERE type='table' AND name=?
                        ''', (table_name,))
                        
                        if cursor.fetchone():
                            # Áp dụng filter thời gian nếu cần
                            date_condition = self._get_date_condition(table_name, date_filter)
                            
                            query = f'''
                                SELECT item_id, shop_id, brand, item_name, 
                                       COUNT(*) as frequency,
                                       '{table_name}' as source_table
                                FROM {table_name}
                                WHERE time_hour = ?
                                {date_condition}
                                GROUP BY item_id, shop_id, brand, item_name
                            '''
                            union_queries.append(query)
                    
                    if union_queries:
                        # Kết hợp tất cả queries
                        full_query = f'''
                            SELECT item_id, shop_id, brand, item_name,
                                   SUM(frequency) as total_frequency,
                                   GROUP_CONCAT(DISTINCT source_table) as tables
                            FROM (
                                {' UNION ALL '.join(union_queries)}
                            )
                            GROUP BY item_id, shop_id, brand, item_name
                            ORDER BY total_frequency DESC
                            LIMIT ?
                        '''
                        
                        # Execute query
                        params = [query_hour] * len(union_queries) + [limit]
                        cursor.execute(full_query, params)
                        
                        rows = cursor.fetchall()
                        results[hour] = [
                            {
                                'item_id': row[0],
                                'shop_id': row[1],
                                'brand': row[2],
                                'item_name': row[3],
                                'frequency': row[4],
                                'source_tables': row[5]
                            }
                            for row in rows
                        ]
                    else:
                        results[hour] = []
                        
        except Exception as e:
            logging.error(f"Error querying top items: {e}")
            results = {hour: [] for hour in time_hours}
        
        return results
    
    def _get_date_condition(self, table_name: str, date_filter: str) -> str:
        """Tạo điều kiện filter theo thời gian"""
        if date_filter == "all":
            return ""
        
        try:
            # Lấy thông tin bảng từ metadata
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT livestream_date FROM table_metadata 
                    WHERE table_name = ?
                ''', (table_name,))
                
                result = cursor.fetchone()
                if not result:
                    return ""
                
                table_date = datetime.strptime(result[0], '%Y-%m-%d')
                current_date = datetime.now()
                
                if date_filter == "3months":
                    cutoff_date = current_date - timedelta(days=90)
                elif date_filter == "6months":
                    cutoff_date = current_date - timedelta(days=180)
                elif date_filter == "1year":
                    cutoff_date = current_date - timedelta(days=365)
                elif date_filter == "same_period_last_year":
                    # Cùng kỳ năm trước (±30 ngày)
                    last_year_date = table_date.replace(year=table_date.year - 1)
                    start_date = last_year_date - timedelta(days=30)
                    end_date = last_year_date + timedelta(days=30)
                    
                    if start_date <= table_date <= end_date:
                        return ""  # Bảng này nằm trong khoảng cùng kỳ năm trước
                    else:
                        return " AND 1=0"  # Loại bỏ bảng này
                else:
                    return ""
                
                # Kiểm tra bảng có nằm trong khoảng thời gian không
                if table_date >= cutoff_date:
                    return ""
                else:
                    return " AND 1=0"  # Loại bỏ bảng này
                    
        except Exception as e:
            logging.error(f"Error creating date condition: {e}")
            return ""
    
    def get_available_kols(self) -> List[str]:
        """Lấy danh sách KOL có sẵn"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT DISTINCT kol_name FROM table_metadata
                    ORDER BY kol_name
                ''')
                
                return [row[0] for row in cursor.fetchall()]
        except Exception as e:
            logging.error(f"Error getting KOLs: {e}")
            return []
    
    def get_date_range_info(self) -> Dict[str, str]:
        """Lấy thông tin về khoảng thời gian dữ liệu"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT MIN(livestream_date) as earliest,
                           MAX(livestream_date) as latest,
                           COUNT(DISTINCT livestream_date) as total_dates
                    FROM table_metadata
                ''')
                
                result = cursor.fetchone()
                if result and result[0]:
                    return {
                        'earliest_date': result[0],
                        'latest_date': result[1],
                        'total_dates': result[2]
                    }
                else:
                    return {
                        'earliest_date': 'N/A',
                        'latest_date': 'N/A', 
                        'total_dates': 0
                    }
        except Exception as e:
            logging.error(f"Error getting date range: {e}")
            return {
                'earliest_date': 'N/A',
                'latest_date': 'N/A',
                'total_dates': 0
            }
