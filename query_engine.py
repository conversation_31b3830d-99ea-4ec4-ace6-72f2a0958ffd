import sqlite3
from datetime import datetime, timedelta
from typing import List, Dict, Tu<PERSON>, Optional
import logging

class QueryEngine:
    def __init__(self, db_path: str):
        self.db_path = db_path
    
    def get_top_items_by_timeframe(self,
                                   table_names: List[str],
                                   time_hours: List[int],
                                   date_filter: str = "all",
                                   limit: int = 400) -> Dict[int, List[Dict]]:
        """
        Lấy top items theo khung giờ với GMV scoring

        Args:
            table_names: Danh sách tên bảng
            time_hours: <PERSON>h sách giờ cần query (10-24)
            date_filter: "3months", "6months", "1year", "same_period_last_year", "all"
            limit: Số lượng items tối đa (default 400)

        Returns:
            Dict với key là giờ, value là list items được sắp xếp theo shop
        """
        results = {}

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                for hour in time_hours:
                    query_hour = 24 if hour == 0 else hour

                    # <PERSON><PERSON><PERSON> dữ liệu từ tất cả bảng
                    all_items = []

                    for table_name in table_names:
                        # Kiểm tra bảng có tồn tại không
                        cursor.execute('''
                            SELECT name FROM sqlite_master
                            WHERE type='table' AND name=?
                        ''', (table_name,))

                        if not cursor.fetchone():
                            continue

                        # Áp dụng filter thời gian
                        if not self._should_include_table(table_name, date_filter):
                            continue

                        # Lấy thông tin cột thời gian
                        time_columns_info = self._get_time_columns_info(table_name)
                        if not time_columns_info:
                            continue

                        # Tìm cột GMV tương ứng với giờ
                        target_columns = []
                        for col_info in time_columns_info:
                            if col_info['hour'] == query_hour:
                                target_columns.append(col_info['column_name'])

                        if not target_columns:
                            continue

                        # Query dữ liệu
                        gmv_columns_sql = ' + '.join([f'COALESCE({col}, 0)' for col in target_columns])

                        cursor.execute(f'''
                            SELECT item_id, shop_id, brand, item_name,
                                   ({gmv_columns_sql}) as total_gmv
                            FROM {table_name}
                            WHERE ({gmv_columns_sql}) > 0
                        ''')

                        rows = cursor.fetchall()
                        for row in rows:
                            all_items.append({
                                'item_id': row[0],
                                'shop_id': row[1],
                                'brand': row[2],
                                'item_name': row[3],
                                'gmv': row[4],
                                'source_table': table_name
                            })

                    # Tính điểm trung bình cho mỗi item
                    item_scores = {}
                    for item in all_items:
                        key = (item['item_id'], item['shop_id'])
                        if key not in item_scores:
                            item_scores[key] = {
                                'item_id': item['item_id'],
                                'shop_id': item['shop_id'],
                                'brand': item['brand'],
                                'item_name': item['item_name'],
                                'total_gmv': 0,
                                'count': 0,
                                'sources': []
                            }

                        item_scores[key]['total_gmv'] += item['gmv']
                        item_scores[key]['count'] += 1
                        item_scores[key]['sources'].append(item['source_table'])

                    # Tính điểm trung bình và sắp xếp
                    scored_items = []
                    for item_data in item_scores.values():
                        avg_score = item_data['total_gmv'] / item_data['count']
                        scored_items.append({
                            'item_id': item_data['item_id'],
                            'shop_id': item_data['shop_id'],
                            'brand': item_data['brand'],
                            'item_name': item_data['item_name'],
                            'avg_gmv': avg_score,
                            'total_gmv': item_data['total_gmv'],
                            'frequency': item_data['count'],
                            'source_tables': ','.join(set(item_data['sources']))
                        })

                    # Sắp xếp theo GMV trung bình
                    scored_items.sort(key=lambda x: x['avg_gmv'], reverse=True)

                    # Lấy top items và sắp xếp theo shop
                    top_items = scored_items[:limit]
                    grouped_by_shop = self._group_by_shop_order(top_items)

                    results[hour] = grouped_by_shop

        except Exception as e:
            logging.error(f"Error querying top items: {e}")
            results = {hour: [] for hour in time_hours}

        return results
    
    def _get_time_columns_info(self, table_name: str) -> List[Dict]:
        """Lấy thông tin các cột thời gian của bảng"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(f'''
                    SELECT column_name, hour, original_header
                    FROM {table_name}_time_columns
                    ORDER BY hour
                ''')

                results = cursor.fetchall()
                return [
                    {
                        'column_name': row[0],
                        'hour': row[1],
                        'original_header': row[2]
                    }
                    for row in results
                ]
        except Exception as e:
            logging.error(f"Error getting time columns for {table_name}: {e}")
            return []

    def _group_by_shop_order(self, items: List[Dict]) -> List[Dict]:
        """Sắp xếp items theo thứ tự shop (nhóm cùng shop lại với nhau)"""
        if not items:
            return []

        # Tạo dict để track thứ tự xuất hiện của shop
        shop_order = {}
        shop_items = {}

        for item in items:
            shop_id = item['shop_id']

            # Ghi nhận thứ tự xuất hiện đầu tiên của shop
            if shop_id not in shop_order:
                shop_order[shop_id] = len(shop_order)
                shop_items[shop_id] = []

            shop_items[shop_id].append(item)

        # Sắp xếp lại theo thứ tự shop xuất hiện
        result = []
        for shop_id in sorted(shop_order.keys(), key=lambda x: shop_order[x]):
            result.extend(shop_items[shop_id])

        return result

    def _should_include_table(self, table_name: str, date_filter: str) -> bool:
        """Kiểm tra bảng có nên được include theo filter thời gian không"""
        if date_filter == "all":
            return True

        try:
            # Lấy thông tin bảng từ metadata
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT livestream_date FROM table_metadata
                    WHERE table_name = ?
                ''', (table_name,))

                result = cursor.fetchone()
                if not result:
                    return False

                table_date = datetime.strptime(result[0], '%Y-%m-%d')
                current_date = datetime.now()

                if date_filter == "3months":
                    cutoff_date = current_date - timedelta(days=90)
                    return table_date >= cutoff_date
                elif date_filter == "6months":
                    cutoff_date = current_date - timedelta(days=180)
                    return table_date >= cutoff_date
                elif date_filter == "1year":
                    cutoff_date = current_date - timedelta(days=365)
                    return table_date >= cutoff_date
                elif date_filter == "same_period_last_year":
                    # Cùng kỳ năm trước (±30 ngày)
                    last_year_date = table_date.replace(year=table_date.year - 1)
                    start_date = last_year_date - timedelta(days=30)
                    end_date = last_year_date + timedelta(days=30)
                    return start_date <= table_date <= end_date
                else:
                    return True

        except Exception as e:
            logging.error(f"Error checking table inclusion: {e}")
            return False
    
    def get_available_kols(self) -> List[str]:
        """Lấy danh sách KOL có sẵn"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT DISTINCT kol_name FROM table_metadata
                    ORDER BY kol_name
                ''')
                
                return [row[0] for row in cursor.fetchall()]
        except Exception as e:
            logging.error(f"Error getting KOLs: {e}")
            return []
    
    def get_date_range_info(self) -> Dict[str, str]:
        """Lấy thông tin về khoảng thời gian dữ liệu"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT MIN(livestream_date) as earliest,
                           MAX(livestream_date) as latest,
                           COUNT(DISTINCT livestream_date) as total_dates
                    FROM table_metadata
                ''')
                
                result = cursor.fetchone()
                if result and result[0]:
                    return {
                        'earliest_date': result[0],
                        'latest_date': result[1],
                        'total_dates': result[2]
                    }
                else:
                    return {
                        'earliest_date': 'N/A',
                        'latest_date': 'N/A', 
                        'total_dates': 0
                    }
        except Exception as e:
            logging.error(f"Error getting date range: {e}")
            return {
                'earliest_date': 'N/A',
                'latest_date': 'N/A',
                'total_dates': 0
            }
