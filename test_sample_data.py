#!/usr/bin/env python3
"""
Script tạo dữ liệu mẫu để test chương trình
"""

import pandas as pd
import random
from datetime import datetime, timedelta
import os

def create_sample_data():
    """Tạo file CSV mẫu với dữ liệu livestream"""
    
    # Tạo dữ liệu mẫu
    sample_data = []
    
    # Tạo 1000 bản ghi mẫu
    for i in range(1000):
        item_id = f"ITEM_{random.randint(100000, 999999)}"
        shop_id = f"SHOP_{random.randint(1000, 9999)}"
        brands = ["Nike", "Adidas", "Samsung", "Apple", "Xiaomi", "Uniqlo", "H&M", "Zara"]
        brand = random.choice(brands)
        
        item_names = [
            f"{brand} Áo thun nam nữ basic",
            f"{brand} Giày thể thao chạy bộ",
            f"{brand} Điện thoại smartphone",
            f"{brand} Tai nghe bluetooth",
            f"{brand} Quần jean slim fit",
            f"{brand} Túi xách thời trang"
        ]
        item_name = random.choice(item_names)
        
        # Tạo thời gian random từ 10:00 đến 23:59
        hours = list(range(10, 24)) + [0]  # 10-23 và 0 (midnight)
        time_hour = random.choice(hours)
        minutes = random.randint(0, 59)
        time_str = f"{time_hour:02d}:{minutes:02d}"
        
        sample_data.append({
            'Item ID': item_id,
            'Shop ID': shop_id,
            'Brand': brand,
            'Item name': item_name,
            'Time': time_str
        })
    
    # Tạo DataFrame và lưu file
    df = pd.DataFrame(sample_data)
    
    # Tạo thư mục sample_data nếu chưa có
    os.makedirs('sample_data', exist_ok=True)
    
    # Lưu file CSV
    csv_file = 'sample_data/livestream_sample.csv'
    df.to_csv(csv_file, index=False, encoding='utf-8-sig')
    print(f"Đã tạo file CSV mẫu: {csv_file}")
    
    # Lưu file Excel
    excel_file = 'sample_data/livestream_sample.xlsx'
    df.to_excel(excel_file, index=False)
    print(f"Đã tạo file Excel mẫu: {excel_file}")
    
    return csv_file, excel_file

def create_multiple_kol_data():
    """Tạo dữ liệu cho nhiều KOL khác nhau"""
    
    kols = ["KOL_A", "KOL_B", "KOL_C"]
    dates = [
        datetime.now() - timedelta(days=30),
        datetime.now() - timedelta(days=15),
        datetime.now() - timedelta(days=7),
        datetime.now() - timedelta(days=1)
    ]
    
    for kol in kols:
        for date in dates:
            # Tạo dữ liệu cho mỗi KOL và ngày
            sample_data = []
            
            for i in range(500):  # 500 items per KOL per date
                item_id = f"{kol}_ITEM_{random.randint(100000, 999999)}"
                shop_id = f"SHOP_{random.randint(1000, 9999)}"
                brands = ["Nike", "Adidas", "Samsung", "Apple", "Xiaomi"]
                brand = random.choice(brands)
                item_name = f"{brand} Product {random.randint(1, 100)}"
                
                # Tạo thời gian với bias cho một số khung giờ
                if random.random() < 0.3:  # 30% trong khung giờ prime time
                    time_hour = random.choice([19, 20, 21, 22])
                else:
                    time_hour = random.choice(list(range(10, 24)) + [0])
                
                minutes = random.randint(0, 59)
                time_str = f"{time_hour:02d}:{minutes:02d}"
                
                sample_data.append({
                    'Item ID': item_id,
                    'Shop ID': shop_id,
                    'Brand': brand,
                    'Item name': item_name,
                    'Time': time_str
                })
            
            # Lưu file
            df = pd.DataFrame(sample_data)
            date_str = date.strftime('%Y-%m-%d')
            filename = f'sample_data/{kol}_{date_str}.csv'
            df.to_csv(filename, index=False, encoding='utf-8-sig')
            print(f"Đã tạo file: {filename}")

if __name__ == "__main__":
    print("Tạo dữ liệu mẫu cho test...")
    
    # Tạo dữ liệu mẫu cơ bản
    create_sample_data()
    
    # Tạo dữ liệu cho nhiều KOL
    create_multiple_kol_data()
    
    print("\nHoàn tất tạo dữ liệu mẫu!")
    print("Bạn có thể sử dụng các file trong thư mục 'sample_data' để test chương trình.")
