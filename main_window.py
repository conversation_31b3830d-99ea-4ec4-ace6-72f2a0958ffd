import sys
from PyQt6.QtWidgets import (QA<PERSON>lication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QGridLayout, QPushButton, QLineEdit, 
                             QLabel, QComboBox, QDateEdit, QTextEdit, QProgressBar,
                             QFileDialog, QMessageBox, QListWidget, QListWidgetItem,
                             QCheckBox, QGroupBox, QSplitter, QTabWidget)
from PyQt6.QtCore import Qt, QDate, pyqtSlot
from PyQt6.QtGui import QFont, QClipboard
from datetime import datetime
import os

from database_manager import DatabaseManager
from query_engine import QueryEngine
from worker_threads import ImportWorker, QueryWorker

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Data All in One - Livestream Analytics")
        self.setGeometry(100, 100, 1400, 900)
        
        # Initialize components
        self.database_manager = DatabaseManager()
        self.query_engine = QueryEngine(self.database_manager.db_path)
        
        # Workers
        self.import_worker = None
        self.query_worker = None
        
        # Setup UI
        self.setup_ui()
        self.setup_connections()
        
        # Load initial data
        self.refresh_kol_list()
        
    def setup_ui(self):
        """Thiết lập giao diện người dùng"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Left panel - Controls
        left_panel = self.create_left_panel()
        
        # Right panel - Results
        right_panel = self.create_right_panel()
        
        # Splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([400, 1000])
        
        main_layout.addWidget(splitter)
        
    def create_left_panel(self):
        """Tạo panel điều khiển bên trái"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Import section
        import_group = QGroupBox("Import Dữ Liệu")
        import_layout = QVBoxLayout(import_group)
        
        # File selection
        file_layout = QHBoxLayout()
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("Chọn file CSV hoặc Excel...")
        self.browse_button = QPushButton("Chọn File")
        file_layout.addWidget(self.file_path_edit)
        file_layout.addWidget(self.browse_button)
        import_layout.addLayout(file_layout)
        
        # KOL name
        kol_layout = QHBoxLayout()
        kol_layout.addWidget(QLabel("Tên KOL:"))
        self.kol_name_edit = QLineEdit()
        kol_layout.addWidget(self.kol_name_edit)
        import_layout.addLayout(kol_layout)
        
        # Livestream date
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("Ngày Livestream:"))
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        date_layout.addWidget(self.date_edit)
        import_layout.addLayout(date_layout)
        
        # Import button
        self.import_button = QPushButton("Import Dữ Liệu")
        import_layout.addWidget(self.import_button)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        import_layout.addWidget(self.progress_bar)
        
        layout.addWidget(import_group)
        
        # Query section
        query_group = QGroupBox("Phân Tích Dữ Liệu")
        query_layout = QVBoxLayout(query_group)
        
        # KOL selection for query
        kol_query_layout = QHBoxLayout()
        kol_query_layout.addWidget(QLabel("KOL:"))
        self.kol_query_edit = QLineEdit()
        self.kol_query_edit.setPlaceholderText("Nhập tên KOL hoặc 'all'")
        kol_query_layout.addWidget(self.kol_query_edit)
        query_layout.addLayout(kol_query_layout)
        
        # Time period filter
        period_layout = QHBoxLayout()
        period_layout.addWidget(QLabel("Khoảng thời gian:"))
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "Tất cả", "3 tháng gần nhất", "6 tháng gần nhất", 
            "1 năm gần nhất", "Cùng kỳ năm trước"
        ])
        period_layout.addWidget(self.period_combo)
        query_layout.addLayout(period_layout)
        
        # Time hours selection
        hours_label = QLabel("Khung giờ:")
        query_layout.addWidget(hours_label)
        
        self.hours_widget = self.create_hours_selection()
        query_layout.addWidget(self.hours_widget)
        
        # Query button
        self.query_button = QPushButton("Phân Tích")
        query_layout.addWidget(self.query_button)
        
        layout.addWidget(query_group)
        
        # Status
        self.status_label = QLabel("Sẵn sàng")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        return panel
        
    def create_hours_selection(self):
        """Tạo widget chọn khung giờ"""
        widget = QWidget()
        layout = QGridLayout(widget)
        
        self.hour_checkboxes = {}
        
        # Tạo checkbox cho mỗi khung giờ từ 10:00 đến 00:00
        hours = list(range(10, 24)) + [0]  # 10-23, 0 (midnight)
        
        for i, hour in enumerate(hours):
            next_hour = (hour + 1) % 24
            text = f"{hour:02d}:00-{next_hour:02d}:00"
            
            checkbox = QCheckBox(text)
            self.hour_checkboxes[hour] = checkbox
            
            row = i // 3
            col = i % 3
            layout.addWidget(checkbox, row, col)
        
        return widget
        
    def create_right_panel(self):
        """Tạo panel kết quả bên phải"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Results tabs
        self.results_tabs = QTabWidget()
        layout.addWidget(self.results_tabs)
        
        # Copy button
        button_layout = QHBoxLayout()
        self.copy_button = QPushButton("Copy Kết Quả")
        self.copy_button.setEnabled(False)
        button_layout.addWidget(self.copy_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        return panel

    def setup_connections(self):
        """Thiết lập kết nối signals và slots"""
        self.browse_button.clicked.connect(self.browse_file)
        self.import_button.clicked.connect(self.import_data)
        self.query_button.clicked.connect(self.query_data)
        self.copy_button.clicked.connect(self.copy_results)

    def browse_file(self):
        """Chọn file để import"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Chọn file dữ liệu", "",
            "CSV Files (*.csv);;Excel Files (*.xlsx *.xls)"
        )
        if file_path:
            self.file_path_edit.setText(file_path)

    def import_data(self):
        """Import dữ liệu từ file"""
        file_path = self.file_path_edit.text().strip()
        kol_name = self.kol_name_edit.text().strip()
        livestream_date = self.date_edit.date().toString("yyyy-MM-dd")

        # Validate inputs
        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "Lỗi", "Vui lòng chọn file hợp lệ")
            return

        if not kol_name:
            QMessageBox.warning(self, "Lỗi", "Vui lòng nhập tên KOL")
            return

        # Disable UI during import
        self.import_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # Start import worker
        self.import_worker = ImportWorker(
            self.database_manager, file_path, kol_name, livestream_date
        )
        self.import_worker.progress_updated.connect(self.progress_bar.setValue)
        self.import_worker.status_updated.connect(self.status_label.setText)
        self.import_worker.import_completed.connect(self.on_import_completed)
        self.import_worker.start()

    @pyqtSlot(bool, str)
    def on_import_completed(self, success, message):
        """Xử lý khi import hoàn tất"""
        self.import_button.setEnabled(True)
        self.progress_bar.setVisible(False)

        if success:
            QMessageBox.information(self, "Thành công", message)
            self.refresh_kol_list()
            self.status_label.setText("Import hoàn tất")
        else:
            QMessageBox.critical(self, "Lỗi", message)
            self.status_label.setText("Import thất bại")

    def query_data(self):
        """Thực hiện query dữ liệu"""
        kol_name = self.kol_query_edit.text().strip()
        if not kol_name:
            QMessageBox.warning(self, "Lỗi", "Vui lòng nhập tên KOL")
            return

        # Get selected hours
        selected_hours = []
        for hour, checkbox in self.hour_checkboxes.items():
            if checkbox.isChecked():
                selected_hours.append(hour)

        if not selected_hours:
            QMessageBox.warning(self, "Lỗi", "Vui lòng chọn ít nhất một khung giờ")
            return

        # Get date filter
        period_map = {
            "Tất cả": "all",
            "3 tháng gần nhất": "3months",
            "6 tháng gần nhất": "6months",
            "1 năm gần nhất": "1year",
            "Cùng kỳ năm trước": "same_period_last_year"
        }
        date_filter = period_map[self.period_combo.currentText()]

        # Get table names
        table_names = self.database_manager.get_tables_by_kol(kol_name)
        if not table_names:
            QMessageBox.warning(self, "Lỗi", f"Không tìm thấy dữ liệu cho KOL: {kol_name}")
            return

        # Disable UI during query
        self.query_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # Start query worker
        self.query_worker = QueryWorker(
            self.query_engine, table_names, selected_hours, date_filter
        )
        self.query_worker.progress_updated.connect(self.progress_bar.setValue)
        self.query_worker.status_updated.connect(self.status_label.setText)
        self.query_worker.query_completed.connect(self.on_query_completed)
        self.query_worker.start()

    @pyqtSlot(dict)
    def on_query_completed(self, results):
        """Xử lý khi query hoàn tất"""
        self.query_button.setEnabled(True)
        self.progress_bar.setVisible(False)

        if results:
            self.display_results(results)
            self.copy_button.setEnabled(True)
            self.status_label.setText("Phân tích hoàn tất")
        else:
            QMessageBox.information(self, "Thông báo", "Không có dữ liệu phù hợp")
            self.status_label.setText("Không có kết quả")

    def display_results(self, results):
        """Hiển thị kết quả phân tích"""
        # Clear existing tabs
        self.results_tabs.clear()
        self.current_results = results

        for hour in sorted(results.keys()):
            items = results[hour]
            if items:
                next_hour = (hour + 1) % 24
                tab_name = f"{hour:02d}:00-{next_hour:02d}:00 ({len(items)} items)"

                # Create tab content
                tab_widget = QTextEdit()
                tab_widget.setReadOnly(True)
                tab_widget.setFont(QFont("Consolas", 10))

                # Format content
                content = []
                for i, item in enumerate(items, 1):
                    content.append(f"{i:3d}. {item['item_id']}")

                tab_widget.setText("\n".join(content))
                self.results_tabs.addTab(tab_widget, tab_name)

    def copy_results(self):
        """Copy kết quả vào clipboard"""
        if not hasattr(self, 'current_results'):
            return

        current_tab = self.results_tabs.currentIndex()
        if current_tab < 0:
            return

        # Get current tab hour
        hours = sorted(self.current_results.keys())
        if current_tab < len(hours):
            hour = hours[current_tab]
            items = self.current_results[hour]

            # Format for clipboard (one ID per line)
            clipboard_text = "\n".join([item['item_id'] for item in items])

            # Copy to clipboard
            clipboard = QApplication.clipboard()
            clipboard.setText(clipboard_text)

            QMessageBox.information(self, "Thành công",
                                  f"Đã copy {len(items)} Item ID vào clipboard")

    def refresh_kol_list(self):
        """Refresh danh sách KOL có sẵn"""
        kols = self.query_engine.get_available_kols()
        # Update placeholder text with available KOLs
        if kols:
            kol_list = ", ".join(kols[:5])  # Show first 5 KOLs
            if len(kols) > 5:
                kol_list += "..."
            self.kol_query_edit.setPlaceholderText(f"Có sẵn: {kol_list}, hoặc 'all'")
