import sys
from PyQt6.QtWidgets import (Q<PERSON><PERSON>lication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QGridLayout, QPushButton, QLineEdit,
                             QLabel, QComboBox, QDateEdit, QTextEdit, QProgressBar,
                             QFileDialog, QMessageBox, QListWidget, QListWidgetItem,
                             QCheckBox, QGroupBox, QSplitter, QTabWidget, QTableWidget,
                             QTableWidgetItem, QHeaderView, QAbstractItemView, QMenu)
from PyQt6.QtCore import Qt, QDate, pyqtSlot
from PyQt6.QtGui import QFont, QClipboard, QKeySequence, QShortcut
from datetime import datetime
import os

from database_manager import DatabaseManager
from query_engine import QueryEngine
from worker_threads import ImportWorker, QueryWorker

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Data All in One - Livestream Analytics")
        self.setGeometry(100, 100, 1400, 900)
        
        # Initialize components
        self.database_manager = DatabaseManager()
        self.query_engine = QueryEngine(self.database_manager.db_path)
        
        # Workers
        self.import_worker = None
        self.query_worker = None
        
        # Setup UI
        self.setup_ui()
        self.setup_connections()
        
        # Load initial data
        self.refresh_kol_list()
        
    def setup_ui(self):
        """Thiết lập giao diện người dùng"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QHBoxLayout(central_widget)
        
        # Left panel - Controls
        left_panel = self.create_left_panel()
        
        # Right panel - Results
        right_panel = self.create_right_panel()
        
        # Splitter
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([400, 1000])
        
        main_layout.addWidget(splitter)
        
    def create_left_panel(self):
        """Tạo panel điều khiển bên trái"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Import section
        import_group = QGroupBox("Import Dữ Liệu")
        import_layout = QVBoxLayout(import_group)
        
        # File selection
        file_layout = QHBoxLayout()
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setPlaceholderText("Chọn file CSV hoặc Excel...")
        self.browse_button = QPushButton("Chọn File")
        file_layout.addWidget(self.file_path_edit)
        file_layout.addWidget(self.browse_button)
        import_layout.addLayout(file_layout)
        
        # KOL name
        kol_layout = QHBoxLayout()
        kol_layout.addWidget(QLabel("Tên KOL:"))
        self.kol_name_edit = QLineEdit()
        kol_layout.addWidget(self.kol_name_edit)
        import_layout.addLayout(kol_layout)
        
        # Livestream date
        date_layout = QHBoxLayout()
        date_layout.addWidget(QLabel("Ngày Livestream:"))
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setCalendarPopup(True)
        date_layout.addWidget(self.date_edit)
        import_layout.addLayout(date_layout)
        
        # Import button
        self.import_button = QPushButton("Import Dữ Liệu")
        import_layout.addWidget(self.import_button)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        import_layout.addWidget(self.progress_bar)
        
        layout.addWidget(import_group)
        
        # Query section
        query_group = QGroupBox("Phân Tích Dữ Liệu")
        query_layout = QVBoxLayout(query_group)
        
        # KOL selection for query
        kol_query_layout = QHBoxLayout()
        kol_query_layout.addWidget(QLabel("KOL:"))
        self.kol_query_edit = QLineEdit()
        self.kol_query_edit.setPlaceholderText("Nhập tên KOL hoặc 'all'")
        kol_query_layout.addWidget(self.kol_query_edit)
        query_layout.addLayout(kol_query_layout)
        
        # Time period filter
        period_layout = QHBoxLayout()
        period_layout.addWidget(QLabel("Khoảng thời gian:"))
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "Tất cả", "3 tháng gần nhất", "6 tháng gần nhất", 
            "1 năm gần nhất", "Cùng kỳ năm trước"
        ])
        period_layout.addWidget(self.period_combo)
        query_layout.addLayout(period_layout)
        
        # Time hours selection
        hours_label = QLabel("Khung giờ:")
        query_layout.addWidget(hours_label)
        
        self.hours_widget = self.create_hours_selection()
        query_layout.addWidget(self.hours_widget)
        
        # Query button
        self.query_button = QPushButton("Phân Tích")
        query_layout.addWidget(self.query_button)
        
        layout.addWidget(query_group)
        
        # Status
        self.status_label = QLabel("Sẵn sàng")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        return panel
        
    def create_hours_selection(self):
        """Tạo widget chọn khung giờ"""
        widget = QWidget()
        layout = QGridLayout(widget)
        
        self.hour_checkboxes = {}
        
        # Tạo checkbox cho mỗi khung giờ từ 10:00 đến 00:00
        hours = list(range(10, 24)) + [0]  # 10-23, 0 (midnight)
        
        for i, hour in enumerate(hours):
            next_hour = (hour + 1) % 24
            text = f"{hour:02d}:00-{next_hour:02d}:00"
            
            checkbox = QCheckBox(text)
            self.hour_checkboxes[hour] = checkbox
            
            row = i // 3
            col = i % 3
            layout.addWidget(checkbox, row, col)
        
        return widget
        
    def create_right_panel(self):
        """Tạo panel kết quả bên phải"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Results tabs
        self.results_tabs = QTabWidget()
        layout.addWidget(self.results_tabs)
        
        # Copy button
        button_layout = QHBoxLayout()
        self.copy_button = QPushButton("Copy Kết Quả")
        self.copy_button.setEnabled(False)
        button_layout.addWidget(self.copy_button)
        button_layout.addStretch()
        layout.addLayout(button_layout)
        
        return panel

    def setup_connections(self):
        """Thiết lập kết nối signals và slots"""
        self.browse_button.clicked.connect(self.browse_file)
        self.import_button.clicked.connect(self.import_data)
        self.query_button.clicked.connect(self.query_data)
        self.copy_button.clicked.connect(self.copy_results)

    def browse_file(self):
        """Chọn file để import"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Chọn file dữ liệu", "",
            "CSV Files (*.csv);;Excel Files (*.xlsx *.xls)"
        )
        if file_path:
            self.file_path_edit.setText(file_path)

    def import_data(self):
        """Import dữ liệu từ file"""
        file_path = self.file_path_edit.text().strip()
        kol_name = self.kol_name_edit.text().strip()
        livestream_date = self.date_edit.date().toString("yyyy-MM-dd")

        # Validate inputs
        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "Lỗi", "Vui lòng chọn file hợp lệ")
            return

        if not kol_name:
            QMessageBox.warning(self, "Lỗi", "Vui lòng nhập tên KOL")
            return

        # Disable UI during import
        self.import_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # Start import worker
        self.import_worker = ImportWorker(
            self.database_manager, file_path, kol_name, livestream_date
        )
        self.import_worker.progress_updated.connect(self.progress_bar.setValue)
        self.import_worker.status_updated.connect(self.status_label.setText)
        self.import_worker.import_completed.connect(self.on_import_completed)
        self.import_worker.start()

    @pyqtSlot(bool, str)
    def on_import_completed(self, success, message):
        """Xử lý khi import hoàn tất"""
        self.import_button.setEnabled(True)
        self.progress_bar.setVisible(False)

        if success:
            QMessageBox.information(self, "Thành công", message)
            self.refresh_kol_list()
            self.status_label.setText("Import hoàn tất")
        else:
            QMessageBox.critical(self, "Lỗi", message)
            self.status_label.setText("Import thất bại")

    def query_data(self):
        """Thực hiện query dữ liệu"""
        kol_name = self.kol_query_edit.text().strip()
        if not kol_name:
            QMessageBox.warning(self, "Lỗi", "Vui lòng nhập tên KOL")
            return

        # Get selected hours
        selected_hours = []
        for hour, checkbox in self.hour_checkboxes.items():
            if checkbox.isChecked():
                selected_hours.append(hour)

        if not selected_hours:
            QMessageBox.warning(self, "Lỗi", "Vui lòng chọn ít nhất một khung giờ")
            return

        # Get date filter
        period_map = {
            "Tất cả": "all",
            "3 tháng gần nhất": "3months",
            "6 tháng gần nhất": "6months",
            "1 năm gần nhất": "1year",
            "Cùng kỳ năm trước": "same_period_last_year"
        }
        date_filter = period_map[self.period_combo.currentText()]

        # Get table names
        table_names = self.database_manager.get_tables_by_kol(kol_name)
        if not table_names:
            QMessageBox.warning(self, "Lỗi", f"Không tìm thấy dữ liệu cho KOL: {kol_name}")
            return

        # Disable UI during query
        self.query_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # Start query worker
        self.query_worker = QueryWorker(
            self.query_engine, table_names, selected_hours, date_filter
        )
        self.query_worker.progress_updated.connect(self.progress_bar.setValue)
        self.query_worker.status_updated.connect(self.status_label.setText)
        self.query_worker.query_completed.connect(self.on_query_completed)
        self.query_worker.start()

    @pyqtSlot(dict)
    def on_query_completed(self, results):
        """Xử lý khi query hoàn tất"""
        self.query_button.setEnabled(True)
        self.progress_bar.setVisible(False)

        if results:
            self.display_results(results)
            self.copy_button.setEnabled(True)
            self.status_label.setText("Phân tích hoàn tất")
        else:
            QMessageBox.information(self, "Thông báo", "Không có dữ liệu phù hợp")
            self.status_label.setText("Không có kết quả")

    def display_results(self, results):
        """Hiển thị kết quả phân tích"""
        # Clear existing tabs
        self.results_tabs.clear()
        self.current_results = results

        for hour in sorted(results.keys()):
            items = results[hour]
            if items:
                next_hour = (hour + 1) % 24
                total_gmv = sum(item.get('avg_gmv', 0) for item in items)
                tab_name = f"{hour:02d}:00-{next_hour:02d}:00 ({len(items)} items) - Total GMV: {total_gmv:,.0f}"

                # Create table widget
                table_widget = QTableWidget()
                table_widget.setRowCount(len(items))
                table_widget.setColumnCount(3)
                table_widget.setHorizontalHeaderLabels(["Item ID", "Item Name", "GMV"])

                # Cấu hình table
                table_widget.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
                table_widget.setSelectionMode(QAbstractItemView.SelectionMode.SingleSelection)
                table_widget.setAlternatingRowColors(True)
                table_widget.setSortingEnabled(False)

                # Cấu hình header
                header = table_widget.horizontalHeader()
                header.setSectionResizeMode(0, QHeaderView.ResizeMode.Fixed)  # Item ID
                header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # Item Name
                header.setSectionResizeMode(2, QHeaderView.ResizeMode.Fixed)  # GMV

                # Đặt độ rộng cột
                table_widget.setColumnWidth(0, 120)  # Item ID
                table_widget.setColumnWidth(2, 120)  # GMV

                # Style cho table
                table_widget.setStyleSheet("""
                    QTableWidget {
                        background-color: #2b2b2b;
                        color: #ffffff;
                        border: 1px solid #555555;
                        gridline-color: #555555;
                        selection-background-color: #404040;
                    }
                    QTableWidget::item {
                        padding: 8px;
                        border-bottom: 1px solid #444444;
                    }
                    QTableWidget::item:selected {
                        background-color: #505050;
                    }
                    QHeaderView::section {
                        background-color: #404040;
                        color: #ffffff;
                        padding: 8px;
                        border: 1px solid #555555;
                        font-weight: bold;
                    }
                """)

                # Điền dữ liệu vào table
                for i, item in enumerate(items):
                    # Item ID - có thể click để copy
                    item_id_cell = QTableWidgetItem(item['item_id'])
                    item_id_cell.setFlags(Qt.ItemFlag.ItemIsEnabled | Qt.ItemFlag.ItemIsSelectable)
                    table_widget.setItem(i, 0, item_id_cell)

                    # Item Name - chỉ đọc
                    item_name_cell = QTableWidgetItem(item.get('item_name', 'N/A'))
                    item_name_cell.setFlags(Qt.ItemFlag.ItemIsEnabled)
                    table_widget.setItem(i, 1, item_name_cell)

                    # GMV - chỉ đọc
                    gmv_value = item.get('avg_gmv', 0)
                    gmv_cell = QTableWidgetItem(f"{gmv_value:,.0f}")
                    gmv_cell.setFlags(Qt.ItemFlag.ItemIsEnabled)
                    table_widget.setItem(i, 2, gmv_cell)

                # Connect events
                table_widget.cellClicked.connect(lambda row, col, table=table_widget: self.on_cell_clicked(table, row, col))

                # Thêm context menu cho copy toàn bộ cột
                table_widget.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
                table_widget.customContextMenuRequested.connect(lambda pos, table=table_widget: self.show_context_menu(table, pos))

                # Thêm phím tắt Ctrl+A để select all và Ctrl+C để copy
                select_all_shortcut = QShortcut(QKeySequence("Ctrl+A"), table_widget)
                select_all_shortcut.activated.connect(lambda table=table_widget: self.select_all_items(table))

                copy_shortcut = QShortcut(QKeySequence("Ctrl+C"), table_widget)
                copy_shortcut.activated.connect(lambda table=table_widget: self.copy_selected_items(table))

                self.results_tabs.addTab(table_widget, tab_name)

    def on_cell_clicked(self, table, row, col):
        """Xử lý khi click vào cell"""
        if col == 0:  # Chỉ xử lý click vào cột Item ID
            item_id = table.item(row, 0).text()
            clipboard = QApplication.clipboard()
            clipboard.setText(item_id)

            # Hiển thị thông báo ngắn
            QMessageBox.information(self, "Copied", f"Đã copy Item ID: {item_id}")

    def show_context_menu(self, table, position):
        """Hiển thị context menu"""
        menu = QMenu()

        copy_all_action = menu.addAction("Copy All Item IDs")
        copy_all_action.triggered.connect(lambda: self.copy_all_item_ids(table))

        # Hiển thị menu tại vị trí click
        menu.exec(table.mapToGlobal(position))

    def copy_all_item_ids(self, table):
        """Copy tất cả Item ID từ table"""
        item_ids = []
        for row in range(table.rowCount()):
            item_id = table.item(row, 0).text()
            item_ids.append(item_id)

        if item_ids:
            clipboard = QApplication.clipboard()
            clipboard.setText('\n'.join(item_ids))
            QMessageBox.information(self, "Copied", f"Đã copy {len(item_ids)} Item IDs vào clipboard")

    def select_all_items(self, table):
        """Select tất cả rows trong table"""
        table.selectAll()

    def copy_selected_items(self, table):
        """Copy các Item ID đã được select"""
        selected_ranges = table.selectedRanges()
        if not selected_ranges:
            # Nếu không có gì được select, copy tất cả
            self.copy_all_item_ids(table)
            return

        item_ids = []
        for selected_range in selected_ranges:
            for row in range(selected_range.topRow(), selected_range.bottomRow() + 1):
                item_id = table.item(row, 0).text()
                if item_id not in item_ids:  # Tránh duplicate
                    item_ids.append(item_id)

        if item_ids:
            clipboard = QApplication.clipboard()
            clipboard.setText('\n'.join(item_ids))
            QMessageBox.information(self, "Copied", f"Đã copy {len(item_ids)} Item IDs vào clipboard")

    def copy_results(self):
        """Copy tất cả Item ID trong tab hiện tại vào clipboard"""
        current_tab_widget = self.results_tabs.currentWidget()
        if not isinstance(current_tab_widget, QTableWidget):
            return

        # Lấy tất cả Item ID từ cột đầu tiên
        item_ids = []
        for row in range(current_tab_widget.rowCount()):
            item_id = current_tab_widget.item(row, 0).text()
            item_ids.append(item_id)

        if item_ids:
            # Copy to clipboard
            clipboard = QApplication.clipboard()
            clipboard.setText('\n'.join(item_ids))

            QMessageBox.information(self, "Thành công",
                                  f"Đã copy {len(item_ids)} Item ID vào clipboard")

    def refresh_kol_list(self):
        """Refresh danh sách KOL có sẵn"""
        kols = self.query_engine.get_available_kols()
        # Update placeholder text with available KOLs
        if kols:
            kol_list = ", ".join(kols[:5])  # Show first 5 KOLs
            if len(kols) > 5:
                kol_list += "..."
            self.kol_query_edit.setPlaceholderText(f"Có sẵn: {kol_list}, hoặc 'all'")
